import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import scipy
import pandas as pd
import numpy as np
from sklearn.decomposition import PCA
from sklearn.model_selection import train_test_split


data=pd.read_csv('2017_Yellow_Taxi_Trip_Data.csv')

datatrainset, datatestset =train_test_split(data,test_size=0.2,random_state=0)

datatrainset.info()

datatrainset.head(10)

datatrainset.shape

datatrainset.columns

pd.set_option('display.float_format', '{:.2f}'.format)
datatrainset.describe()

datatrainset.dtypes

# Afficher le nombre de colonnes par type
print("Types de données :\n\n", datatrainset.dtypes.value_counts())

datatrainset.dtypes.value_counts().plot.pie(autopct='%1.1f%%', startangle=90)
plt.title("Répartition des types de données")
plt.ylabel("")
plt.show()

!pip install missingno

import missingno as msno
msno.bar(datatrainset, color="dodgerblue")
plt.title("Aucune donnée manquante - Barres pleines = 100% de complétude")
plt.show()

msno.matrix(datatrainset)
plt.show()

colonnes_numeriques = datatrainset.select_dtypes(include=['float64', 'int64']).columns
n_colonnes = len(colonnes_numeriques)
n_lignes = (n_colonnes // 3) + (1 if n_colonnes % 3 != 0 else 0)
fig, axs = plt.subplots(n_lignes, 3, figsize=(15, n_lignes * 4))
fig.suptitle("Distributions des variables numériques", y=1.02)
for i, colonne in enumerate(colonnes_numeriques):
    ligne = i // 3
    colonne_plot = i % 3
    sns.histplot(
        datatrainset[colonne],
        bins=20,
        color='skyblue',
        ax=axs[ligne, colonne_plot],
        edgecolor='black',
        kde=True
    )
    axs[ligne, colonne_plot].set_title(colonne)
for j in range(i + 1, n_lignes * 3):
    ligne = j // 3
    colonne_plot = j % 3
    axs[ligne, colonne_plot].axis('off')
plt.tight_layout()
plt.show()

df = datatrainset
for colonne in df.columns:
    print(f"\n=== {colonne} ===")

    unique_values = df[colonne].nunique()

    if df[colonne].dtype == 'object' or unique_values < 10:
        distrib = df[colonne].value_counts(normalize=True, dropna=False)
    else:
        distrib = pd.cut(df[colonne], bins=5).value_counts(normalize=True, dropna=False)
    distrib_non_zero = distrib[distrib > 0]
    if len(distrib_non_zero) == 0:
        print(" Aucune donnée valide dans cette variable.")
        continue
    desequilibre = distrib_non_zero.max() / distrib_non_zero.min()
    print(distrib)
    if desequilibre > 5:
        print(" Déséquilibre extrême (attention aux outliers ou encodage)")
    elif desequilibre > 2:
        print(" Variable légèrement déséquilibrée")
    else:
        print(" Variable équilibrée")



datatrainset['tip_percentage'] = (datatrainset['tip_amount'] / datatrainset['fare_amount']) * 100
datatrainset['is_generous'] = (datatrainset['tip_percentage'] >= 20).astype(int)
print(datatrainset['is_generous'].value_counts(normalize=True))

fig, ax = plt.subplots(figsize=(10, 8))

# Countplot
sns.countplot(x='is_generous', data=datatrainset, ax=ax, palette='Set3', edgecolor='black')

# Titre et axes
ax.set_title('Distribution des identifiants target', fontsize=14, fontweight='bold')
ax.set_xlabel('target', fontsize=12)
ax.set_ylabel('Nombre d\'observations', fontsize=12)

# Rotation des labels
ax.set_xticklabels(ax.get_xticklabels(), rotation=0)

# Affichage des valeurs sur les barres
for p in ax.patches:
    height = p.get_height()
    ax.annotate(f'{height}',
                (p.get_x() + p.get_width() / 2., height),
                ha='center', va='bottom',
                fontsize=10, fontweight='bold')

plt.tight_layout()
plt.show()

# Stats de base
print(datatrainset['is_generous'].value_counts(normalize=True))

target_col = 'is_generous'
distrib = datatrainset['is_generous']
print(f"\nDistribution de la variable cible '{target_col}':")
print(distrib)
desequilibre = distrib.iloc[0] / distrib.iloc[-1]

if desequilibre > 5:
    print(" Déséquilibre extrême - SMOTE fortement conseillé")
elif desequilibre > 2:
    print("Bon candidat pour SMOTE")
else:
    print(" Équilibre correct - Pas besoin de SMOTE")

for col in datatrainset.select_dtypes(include=['object', 'category', 'int64']).columns:
    if datatrainset[col].nunique() < 10:
        cross_tab = pd.crosstab(datatrainset[col], datatrainset['is_generous'], normalize='index')*100
        print(f"\n🔹 {col} vs is_generous:\n", cross_tab)
        plt.figure(figsize=(10, 6))
        sns.histplot(data=datatrainset, x=col, hue='is_generous', multiple='stack', palette=['skyblue', 'salmon'],bins=50)
        plt.title(f"Distribution de {col} par Classe Généreuse")
        plt.xticks(rotation=45)
        plt.show()

def extrairedetime(datatrainset):
    datatrainset["tpep_pickup_datetime"] = pd.to_datetime(datatrainset["tpep_pickup_datetime"])
    datatrainset["tpep_dropoff_datetime"] = pd.to_datetime(datatrainset["tpep_dropoff_datetime"])
    datatrainset["pickup_hour"] = datatrainset["tpep_pickup_datetime"].dt.hour
    datatrainset["pickup_minute"] = datatrainset["tpep_pickup_datetime"].dt.minute
    datatrainset["pickup_second"] = datatrainset["tpep_pickup_datetime"].dt.second
    datatrainset["trip_duration"] = (datatrainset["tpep_dropoff_datetime"] - datatrainset["tpep_pickup_datetime"]).dt.total_seconds() / 60
    datatrainset["pickup_day"] = datatrainset["tpep_pickup_datetime"].dt.day
    datatrainset["pickup_dayofweek"] = datatrainset["tpep_pickup_datetime"].dt.dayofweek
    datatrainset["pickup_day_name"] = datatrainset["tpep_pickup_datetime"].dt.day_name()
    datatrainset["pickup_week"] = datatrainset["tpep_pickup_datetime"].dt.isocalendar().week
    datatrainset["pickup_month"] = datatrainset["tpep_pickup_datetime"].dt.month
    datatrainset["pickup_month_name"] = datatrainset["tpep_pickup_datetime"].dt.month_name()
    datatrainset["time_of_day"] = datatrainset["pickup_hour"].apply(get_time_of_day)
    return datatrainset
def get_time_of_day(hour):
    if 5 <= hour < 12:
        return "morning"
    elif 12 <= hour < 17:
        return "afternoon"
    elif 17 <= hour < 21:
        return "evening"
    else:
        return "night"

datatrainset=extrairedetime(datatrainset)

plt.figure(figsize=(10, 6))
sns.countplot(x="pickup_hour", data=datatrainset, palette="Blues_r")
plt.title("Nombre de trajets par heure", fontsize=14)
plt.xlabel("Heure", fontsize=12)
plt.ylabel("Nombre de trajets", fontsize=12)
plt.grid(axis="y", linestyle="--", alpha=0.7)
plt.show()


plt.figure(figsize=(10,6))
sns.histplot(datatrainset["trip_duration"], bins=50, kde=True, color="skyblue")
plt.title("Distribution de la durée des trajets (minutes)", fontsize=14)
plt.xlabel("Durée du trajet (min)", fontsize=12)
plt.ylabel("Fréquence", fontsize=12)
plt.grid(True, linestyle="--", alpha=0.6)
plt.tight_layout()
plt.show()

plt.figure(figsize=(10, 6))
sns.countplot(x="pickup_day_name", data=datatrainset,
order=["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], palette="pastel")
plt.title("Nombre de trajets par jour de la semaine", fontsize=14)
plt.xlabel("Jour", fontsize=12)
plt.ylabel("Nombre de trajets", fontsize=12)
plt.xticks(rotation=45)
plt.grid(axis="y", alpha=0.7)
plt.show()


plt.figure(figsize=(10,8))
sns.countplot(x="pickup_month_name", data=datatrainset, order=pd.date_range("2023-01-01", "2023-12-01", freq='MS').strftime("%B"), palette="coolwarm")
plt.title("Nombre de trajets par mois", fontsize=14)
plt.xlabel("Mois", fontsize=12)
plt.ylabel("Nombre de trajets", fontsize=12)
plt.xticks(rotation=45)
plt.grid(axis="y", linestyle="--", alpha=0.7)
plt.show()


order = ["morning", "afternoon", "evening", "night"]
plt.figure(figsize=(8,6))
sns.countplot(data=datatrainset, x="time_of_day", order=order, palette="viridis")
plt.title(" Répartition des trajets par période de la journée", fontsize=14)
plt.xlabel("Période de la journée", fontsize=12)
plt.ylabel("Nombre de trajets", fontsize=12)
plt.grid(axis='y', linestyle='--', alpha=0.5)
plt.tight_layout()
plt.show()

datatrainset["pickup_year"] = datatrainset["tpep_pickup_datetime"].dt.year
plt.figure(figsize=(8, 5))
sns.countplot(x="pickup_year", data=datatrainset, palette="Blues")
plt.title("Nombre de trajets par année", fontsize=14)
plt.xlabel("Année", fontsize=12)
plt.ylabel("Nombre de trajets", fontsize=12)
plt.grid(axis="y", linestyle="--", alpha=0.7)
plt.show()

import math
plt.figure(figsize=(15, 20))

colonnes_numeriques = datatrainset.select_dtypes(include=['int64', 'float64']).columns
n=len(colonnes_numeriques)
rows=math.ceil(n/3)
for i, colonne in enumerate(colonnes_numeriques, 1):
    plt.subplot(rows, 3, i)
    sns.boxplot(data=datatrainset, y=colonne, color='skyblue')
    plt.title(f'Boxplot de {colonne}')
    plt.tight_layout()

plt.show()

corr_matrix = datatrainset.select_dtypes(include=['float64', 'int64']).corr()

plt.figure(figsize=(12, 8))
sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, fmt=".2f")
plt.title("Matrice de Corrélation")
plt.show()

def nettoyage(df):
    cols_to_drop = ['extra', 'mta_tax','Unnamed: 0']
    df = df.drop(cols_to_drop, axis=1)
    df=df[
    (df["passenger_count"] != 0) &
    (df["trip_distance"] > 0) &
    (df["fare_amount"] > 0) &
    (df["total_amount"] > 0) &
    (df["pickup_minute"]>0)].copy()
    return df

datatrainset.shape

datatrainset_clean=nettoyage(datatrainset)

datatrainset_clean.shape

datatrainset_clean.info()

def encodage(df):
    code1 = {
        'Y': 1, 'N': 0,
        'night': 1, 'morning': 2, 'afternoon': 3, 'evening': 4,
        'Sunday': 0, 'Monday': 1, 'Tuesday': 2, 'Wednesday': 3,
        'Thursday': 4, 'Friday': 5, 'Saturday': 6,
        'January': 1, 'February': 2, 'March': 3, 'April': 4,
        'May': 5, 'June': 6, 'July': 7, 'August': 8,
        'September': 9, 'October': 10, 'November': 11, 'December': 12
    }

    # Appliquer l'encodage uniquement sur les colonnes object/text
    for col in df.select_dtypes(include='object').columns:
        df[col] = df[col].map(code1)

    return df

datatrainset_clean=encodage(datatrainset_clean)

datatrainset_clean.info()

def FeatureEngineering(datatrainset_clean):
    datatrainset_clean['trip_duration'] = (datatrainset_clean['tpep_dropoff_datetime'] - datatrainset_clean['tpep_pickup_datetime']).dt.total_seconds() / 60
    datatrainset_clean['speed_mph'] = datatrainset_clean['trip_distance'] / (datatrainset_clean['trip_duration'] / 60)
    datatrainset_clean = datatrainset_clean[(datatrainset_clean['speed_mph'] > 0) & (datatrainset_clean['speed_mph'] < 100)]
    return datatrainset_clean
    

datatrainset_clean=FeatureEngineering(datatrainset_clean)

numerical_cols = datatrainset_clean.select_dtypes(include=['float64', 'int64']).columns
print("Numerical columns:", numerical_cols)

from sklearn.preprocessing import StandardScaler
scaler = StandardScaler()
numeric_columns = datatrainset_clean.select_dtypes(include=['float', 'int']).columns
datatrainset_clean[numeric_columns] = scaler.fit_transform(datatrainset_clean[numeric_columns])
print(datatrainset_clean.head())

selected_columns = numeric_columns[:5]
fig, axes = plt.subplots(2, len(selected_columns), figsize=(15, 8))
for i, column in enumerate(selected_columns):
    sns.histplot(datatrainset_clean[column], kde=True, ax=axes[0, i], color='skyblue')
    axes[0, i].set_title(f'{column} avant standardisation')
    sns.histplot(datatrainset_clean[column], kde=True, ax=axes[1, i], color='orange')
    axes[1, i].set_title(f'{column} après standardisation')
plt.tight_layout()
plt.show()

from sklearn.feature_selection import SelectKBest, f_classif
from sklearn.preprocessing import LabelEncoder
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import cross_val_score
X = datatrainset_clean.drop(columns=['is_generous']).copy()
y = datatrainset_clean['is_generous'].copy()
print(f"Unique values in 'is_generous': {y.unique()}")
if y.dtype != 'int' and y.dtype != 'bool':
    y = (y > 0.5).astype(int)
print(f"Target after conversion: {y.unique()}")
X = X.loc[:, X.nunique() > 1]
X = X.dropna()
y = y.loc[X.index]
for col in X.select_dtypes(include=['datetime', 'datetimetz']).columns:
    X[col + '_year'] = X[col].dt.year
    X[col + '_month'] = X[col].dt.month
    X[col + '_day'] = X[col].dt.day
    X.drop(columns=col, inplace=True)

if X.shape[1] == 0:
    raise ValueError("Aucune variable explicative valide après le prétraitement.")

# Sélection de variables (méthode du coude)
scores = []
k_range = range(1, X.shape[1] + 1)
for k in k_range:
    selector = SelectKBest(f_classif, k=k)
    X_selected = selector.fit_transform(X, y)
    cv_scores = cross_val_score(LogisticRegression(max_iter=1000), X_selected, y, cv=5, scoring='accuracy')
    scores.append(cv_scores.mean())

# Visualisation Elbow
optimal_k = k_range[scores.index(max(scores))]
plt.figure(figsize=(10, 6))
plt.plot(k_range, scores, marker='o', linestyle='--', color='b', label='Accuracy')
plt.axvline(optimal_k, color='red', linestyle=':', label=f'Optimal k = {optimal_k}')
plt.title('Elbow Method for Feature Selection')
plt.xlabel('Number of Features (k)')
plt.ylabel('Cross-Validation Accuracy')
plt.grid(True)
plt.legend()
plt.show()

# Sélection finale des variables
selector = SelectKBest(f_classif, k=optimal_k)
X_selected = selector.fit_transform(X, y)
selected_features = X.columns[selector.get_support()]
feature_scores = selector.scores_[selector.get_support()]

# Importance relative
total_score = sum(feature_scores)
importance_percentages = [(s / total_score) * 100 for s in feature_scores]

print(f'\nSelected Features ({optimal_k}):')
for feat, score, percent in zip(selected_features, feature_scores, importance_percentages):
    print(f"{feat}: F-score = {score:.4f}, Importance = {percent:.2f}%")

# Visualisation des F-scores
plt.figure(figsize=(10, 6))
plt.barh(selected_features, feature_scores, color='skyblue')
plt.xlabel('ANOVA F-value')
plt.title('Top Features Selected by SelectKBest')
plt.grid(True)
plt.tight_layout()
plt.show()


from sklearn.decomposition import PCA
numerical_cols = datatrainset_clean.select_dtypes(include=[np.number])
acp = PCA()
acp.fit(numerical_cols)

# Obtenir le ratio de variance expliquée
explained_variance_ratio = acp.explained_variance_ratio_

# Calculer la variance expliquée cumulée
cumulative_variance = explained_variance_ratio.cumsum()

plt.figure(figsize=(8, 5))
plt.plot(range(1, len(cumulative_variance) + 1), cumulative_variance, marker='o', linestyle='--')
plt.xlabel('Number of Components')
plt.ylabel('Cumulative Explained Variance')
plt.title('Choosing the Optimal Number of Components')
plt.grid()
plt.show()

from sklearn.decomposition import PCA
import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt

# Sélectionner uniquement les variables numériques de datatrainset_clean
X = datatrainset_clean.select_dtypes(include=[np.number])  # Variables numériques

# Définir la variable cible (is_generous)
y = datatrainset_clean['is_generous']  # Variable cible

# Appliquer l'ACP uniquement sur les variables numériques
pca = PCA(n_components=2)  # Réduction à 2 dimensions pour la visualisation
X_pca = pca.fit_transform(X)  # Utilisation des features spécifiées dans X

# Créer un DataFrame pour les résultats de l'ACP
pca_df = pd.DataFrame(X_pca, columns=['PC1', 'PC2'])

# Ajout de la variable cible pour colorier selon les classes
pca_df['is_generous'] = y.values  # On ajoute la variable cible à pca_df

# Affichage de la variance expliquée par chaque composante
print("Variance expliquée par chaque composante principale :")
print(pca.explained_variance_ratio_)

# Affichage du cumul de la variance expliquée
print("Variance expliquée cumulée :")
print(np.cumsum(pca.explained_variance_ratio_))

# Visualisation des deux premières composantes principales
sns.scatterplot(x='PC1', y='PC2', hue='is_generous', data=pca_df, palette='viridis', alpha=0.7)
plt.title('Visualisation des deux premières composantes principales')
plt.show()


# Appliquer l'ACP sur toutes les variables numériques
pca = PCA()  # Appliquer l'ACP sans spécifier n_components
pca.fit(X)  # Appliquer l'ACP sur les données

# Affichage de la variance expliquée par chaque composante
plt.figure(figsize=(8, 6))
plt.plot(range(1, len(pca.explained_variance_ratio_) + 1), pca.explained_variance_ratio_, marker='o', linestyle='--', color='b')
plt.title("Variance expliquée par chaque composante principale")
plt.xlabel("Composantes principales")
plt.ylabel("Variance expliquée")
plt.show()


# Affichage de la variance expliquée cumulée
plt.figure(figsize=(8, 6))
plt.plot(range(1, len(pca.explained_variance_ratio_) + 1), np.cumsum(pca.explained_variance_ratio_), marker='o', linestyle='-', color='r')
plt.title("Variance expliquée cumulée")
plt.xlabel("Nombre de composantes principales")
plt.ylabel("Variance expliquée cumulée")
plt.show()


# cette fonction dessine le cercle de correlation .
def display_circles(pcs, n_comp, pca, axis_ranks, labels=None, label_rotation=0, lims=None):
    for d1, d2 in axis_ranks:
        if d2 < n_comp:

            # initialisation de la figure
            fig, ax = plt.subplots(figsize=(10,10))

            # détermination des limites du graphique
            if lims is not None :
                xmin, xmax, ymin, ymax = lims
            elif pcs.shape[1] < 30 :
                xmin, xmax, ymin, ymax = -1, 1, -1, 1
            else :
                xmin, xmax, ymin, ymax = min(pcs[d1,:]), max(pcs[d1,:]), min(pcs[d2,:]), max(pcs[d2,:])

            # affichage des flèches
            # s'il y a plus de 30 flèches, on n'affiche pas le triangle à leur extrémité
            if pcs.shape[1] < 30 :
                plt.quiver(np.zeros(pcs.shape[1]), np.zeros(pcs.shape[1]),
                   pcs[d1,:], pcs[d2,:],
                   angles='xy', scale_units='xy', scale=1, color="grey")
                # (voir la doc : https://matplotlib.org/api/_as_gen/matplotlib.pyplot.quiver.html)
            else:
                lines = [[[0,0],[x,y]] for x,y in pcs[[d1,d2]].T]
                ax.add_collection(LineCollection(lines, axes=ax, alpha=.1, color='black'))

            # affichage des noms des variables
            if labels is not None:
                for i,(x, y) in enumerate(pcs[[d1,d2]].T):
                    if x >= xmin and x <= xmax and y >= ymin and y <= ymax :
                        plt.text(x, y, labels[i], fontsize='14', ha='center', va='center', rotation=label_rotation, color="blue", alpha=0.5)

            # affichage du cercle
            circle = plt.Circle((0,0), 1, facecolor='none', edgecolor='b')
            plt.gca().add_artist(circle)

            # définition des limites du graphique
            plt.xlim(xmin, xmax)
            plt.ylim(ymin, ymax)

            # affichage des lignes horizontales et verticales
            plt.plot([-1, 1], [0, 0], color='grey', ls='--')
            plt.plot([0, 0], [-1, 1], color='grey', ls='--')

            # nom des axes, avec le pourcentage d'inertie expliqué
            plt.xlabel('F{} ({}%)'.format(d1+1, round(100*pca.explained_variance_ratio_[d1],1)))
            plt.ylabel('F{} ({}%)'.format(d2+1, round(100*pca.explained_variance_ratio_[d2],1)))

            plt.title("Cercle des corrélations (F{} et F{})".format(d1+1, d2+1))
            plt.show(block=False)

import matplotlib.cm as cm  # Import the colormap module

composantes = acp.components_

def afficher_circles(pca, df, batch_size=4):
    num_features = df.shape[1]
    colors = cm.viridis(np.linspace(0, 1, num_features))  # Use a colormap for colors
    for i in range(0, num_features, batch_size):
        plt.figure(figsize=(8, 8))
        circle = plt.Circle((0, 0), 1, color='b', fill=False, linestyle='--', linewidth=1)
        plt.gca().add_artist(circle)

        for j in range(i, min(i + batch_size, num_features)):
            plt.quiver(0, 0, composantes[0, j], composantes[1, j], angles='xy', scale_units='xy', scale=1,
                       label=df.columns[j], color=colors[j])

        plt.xlim(-1, 1)
        plt.ylim(-1, 1)
        plt.axhline(0, color='black', linewidth=0.5)
        plt.axvline(0, color='black', linewidth=0.5)

        plt.legend(loc='lower right', bbox_to_anchor=(1.05, 0))  # Move legend to the bottom-right
        plt.title(f'Cercle de Corrélation - PCA (Colonnes {i+1} à {min(i+batch_size, num_features)})')
        plt.gca().set_aspect('equal', adjustable='box')

        plt.grid(True)
        plt.show()

afficher_circles(acp, numerical_cols, batch_size=4)


from sklearn.decomposition import PCA
import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt

# Sélectionner uniquement les variables numériques de datatrainset_clean
X = datatrainset_clean.select_dtypes(include=[np.number])  # Variables numériques

# Définir la variable cible (is_generous)
y = datatrainset_clean['is_generous']  # Variable cible

# Appliquer l'ACP uniquement sur les variables numériques
acp = PCA(n_components=2)  # Réduction à 2 dimensions pour la visualisation
X_pca = acp.fit_transform(X)  # Utilisation des features spécifiées dans X

# Créer un DataFrame pour les résultats de l'ACP
pca_df = pd.DataFrame(X_pca, columns=['PC1', 'PC2'])

# Ajout de la variable cible pour colorier selon les classes
pca_df['is_generous'] = y.values  # On ajoute la variable cible à pca_df

# Affichage de la variance expliquée par chaque composante
print("Variance expliquée par chaque composante principale :")
print(acp.explained_variance_ratio_)

# Affichage du cumul de la variance expliquée
print("Variance expliquée cumulée :")
print(np.cumsum(acp.explained_variance_ratio_))

# Afficher les contributions des features dans chaque composante principale
numerical_cols = X  # Variables numériques pour les labels des features

# Calculer les "loadings" (contributions des features dans chaque composante)
loadings = pd.DataFrame(acp.components_, columns=numerical_cols.columns, index=[f'PC{i+1}' for i in range(acp.n_components_)])

# Convertir les "loadings" en pourcentage pour une meilleure interprétation
pc1_loadings = loadings.loc["PC1"]
pc2_loadings = loadings.loc["PC2"]

# Convertir en valeurs absolues et normaliser pour obtenir les pourcentages
pc1_percent = (np.abs(pc1_loadings) / np.abs(pc1_loadings).sum()) * 100
pc2_percent = (np.abs(pc2_loadings) / np.abs(pc2_loadings).sum()) * 100

# Afficher les contributions des features en pourcentage pour chaque composante
print("\nContributions des features à la composante PC1 (en pourcentage) :")
print(pc1_percent.sort_values(ascending=False))

print("\nContributions des features à la composante PC2 (en pourcentage) :")
print(pc2_percent.sort_values(ascending=False))

# Trier les valeurs de la plus grande à la plus petite
pc1_percent = pc1_percent.sort_values(ascending=False)
pc2_percent = pc2_percent.sort_values(ascending=False)

# Générer des nuances de vert pour les graphiques circulaires
def generate_green_shades(n):
    return plt.cm.Greens(np.linspace(0.3, 1, n))  # Ajuster 0.3 pour contrôler la nuance la plus claire

# Fonction pour tracer un graphique en camembert trié avec des nuances de vert (sans pourcentages)
def plot_pie_chart(data, title):
    colors = generate_green_shades(len(data))  # Générer n nuances de vert
    plt.figure(figsize=(8, 8))
    plt.pie(data, labels=data.index, startangle=140, colors=colors)  # Retirer autopct
    plt.title(title)
    plt.axis('equal')  # Un rapport égal garantit que le graphique est circulaire
    plt.show()

# Tracer les graphiques en camembert triés avec des nuances de vert (sans pourcentages)
plot_pie_chart(pc1_percent, "Contributions des Features à PC1 (Triées, Nuances de Vert)")
plot_pie_chart(pc2_percent, "Contributions des Features à PC2 (Triées, Nuances de Vert)")


# fonction pour afficher les composantes principales
def plot_var_explique (acp):
    var_explique = acp.explained_variance_ratio_
    plt.bar(np.arange(len(var_explique ))+1, var_explique )
    plt.plot(np.arange(len(var_explique ))+1, var_explique .cumsum(),c="red",marker='o')
    plt.xlabel("la rang de l'axe d'inertie")
    plt.ylabel("pourcentage d'inertie")
    plt.title(" Eboulis des valeurs propres")
    plt.show(block=False)

plt.figure( figsize = ( 15, 8))
plot_var_explique (acp)

from sklearn import svm
from sklearn.model_selection import train_test_split
from sklearn.metrics import (accuracy_score, precision_score, recall_score,
                            f1_score, classification_report, confusion_matrix,
                            ConfusionMatrixDisplay, roc_auc_score)
from sklearn.decomposition import PCA
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import train_test_split
from sklearn.metrics import (accuracy_score, precision_score, recall_score,
                            f1_score, classification_report, confusion_matrix,
                            ConfusionMatrixDisplay, roc_auc_score, RocCurveDisplay)
from sklearn.tree import DecisionTreeClassifier, plot_tree, export_text
from sklearn.model_selection import train_test_split
from sklearn.metrics import (accuracy_score, precision_score, recall_score,
                            f1_score, classification_report, confusion_matrix,
                            ConfusionMatrixDisplay, roc_auc_score, RocCurveDisplay)
from sklearn.naive_bayes import GaussianNB
from sklearn.model_selection import train_test_split
from sklearn.metrics import (accuracy_score, precision_score, recall_score,
                            f1_score, classification_report, confusion_matrix,
                            ConfusionMatrixDisplay, roc_auc_score, RocCurveDisplay)
from sklearn.inspection import DecisionBoundaryDisplay

features = ['pickup_week', 'pickup_month', 'fare_amount',
            'trip_distance', 'RatecodeID', 'payment_type', 'speed_mph','PULocationID','DOLocationID']

X = datatrainset_clean[features]
y = datatrainset_clean['is_generous'].astype(int)  

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

model = svm.SVC(kernel='rbf', C=1.0, gamma='scale', probability=True)
model.fit(X_train, y_train)


y_pred = model.predict(X_test)
y_proba = model.predict_proba(X_test)[:, 1]  # Probabilités pour la classe positive

# Calcul des métriques
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred)
recall = recall_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred)
roc_auc = roc_auc_score(y_test, y_proba)

# Affichage des métriques avec une mise en forme claire
print("╔══════════════════════════════╗")
print("║       MÉTRIQUES DU MODÈLE    ║")
print("╠══════════════════════════════╣")
print(f"║ Accuracy: {accuracy:.4f}             ║")
print(f"║ Precision: {precision:.4f}            ║")
print(f"║ Recall: {recall:.4f}               ║")
print(f"║ F1-score: {f1:.4f}             ║")
print(f"║ ROC AUC: {roc_auc:.4f}              ║")
print("╚══════════════════════════════╝")

print("\nClassification Report détaillé:")
print(classification_report(y_test, y_pred))


# 5. Matrice de confusion
cm = confusion_matrix(y_test, y_pred)
disp = ConfusionMatrixDisplay(confusion_matrix=cm)
disp.plot(cmap='Blues')
plt.title("Matrice de Confusion")
plt.show()


from sklearn.model_selection import GridSearchCV
from sklearn.svm import SVC
params_svm = {
    'C': [0.1, 1, 10],
    'kernel': ['linear', 'rbf'],
    'gamma': ['scale', 'auto']
}
grid_svm = GridSearchCV(SVC(probability=True), params_svm, cv=5, scoring='f1')
grid_svm.fit(X_train, y_train)

print("SVM - Meilleurs paramètres :", grid_svm.best_params_)
best_svm = grid_svm.best_estimator_

X = datatrainset_clean[features]
y = datatrainset_clean['is_generous'].astype(int)  

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

model = svm.SVC(kernel='rbf', C=1.0, gamma='scale', probability=True)
model.fit(X_train, y_train)


### y_pred = model.predict(X_test)
y_proba = model.predict_proba(X_test)[:, 1]  # Probabilités pour la classe positive

# Calcul des métriques
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred)
recall = recall_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred)
roc_auc = roc_auc_score(y_test, y_proba)

# Affichage des métriques avec une mise en forme claire
print("╔══════════════════════════════╗")
print("║       MÉTRIQUES DU MODÈLE    ║")
print("╠══════════════════════════════╣")
print(f"║ Accuracy: {accuracy:.4f}             ║")
print(f"║ Precision: {precision:.4f}            ║")
print(f"║ Recall: {recall:.4f}               ║")
print(f"║ F1-score: {f1:.4f}             ║")
print(f"║ ROC AUC: {roc_auc:.4f}              ║")
print("╚══════════════════════════════╝")
print("\nClassification Report détaillé:")
print(classification_report(y_test, y_pred))


# 5. Matrice de confusion
cm = confusion_matrix(y_test, y_pred)
disp = ConfusionMatrixDisplay(confusion_matrix=cm)
disp.plot(cmap='Blues')
plt.title("Matrice de Confusion")
plt.show()


from sklearn.neural_network import MLPClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, classification_report, confusion_matrix, ConfusionMatrixDisplay
import matplotlib.pyplot as plt

# 1. Préparation des données
X = datatrainset_clean[features]
y = datatrainset_clean['is_generous'].astype(int)

# 2. Split des données
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

# 3. Réseau de neurones
model = MLPClassifier(hidden_layer_sizes=(100,), activation='relu', solver='adam', max_iter=300, random_state=42)
model.fit(X_train, y_train)


# 4. Prédiction
y_pred = model.predict(X_test)
y_proba = model.predict_proba(X_test)[:, 1]

# 5. Évaluation
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred)
recall = recall_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred)
roc_auc = roc_auc_score(y_test, y_proba)

# Affichage des métriques
print("╔════════════════════════════════════╗")
print("║ MÉTRIQUES DU RÉSEAU DE NEURONES    ║")
print("╠════════════════════════════════════╣")
print(f"║ Accuracy: {accuracy:.4f}                   ║")
print(f"║ Precision: {precision:.4f}                  ║")
print(f"║ Recall: {recall:.4f}                     ║")
print(f"║ F1-score: {f1:.4f}                   ║")
print(f"║ ROC AUC: {roc_auc:.4f}                    ║")
print("╚════════════════════════════════════╝")

print("\nClassification Report détaillé:")
print(classification_report(y_test, y_pred))

# Matrice de confusion
cm = confusion_matrix(y_pred,y_test)
disp = ConfusionMatrixDisplay(confusion_matrix=cm)
disp.plot(cmap='Purples')
plt.title("Matrice de Confusion - Réseau de Neurones")
plt.show()

from sklearn.model_selection import GridSearchCV
params_mlp = {
    'hidden_layer_sizes': [(50,), (100,), (100, 50)],
    'activation': ['relu', 'tanh'],
    'solver': ['adam'],
    'max_iter': [200, 300]
}
grid_mlp = GridSearchCV(MLPClassifier(random_state=42), params_mlp, cv=5, scoring='f1')
grid_mlp.fit(X_train, y_train)
print("Meilleurs hyperparamètres MLP:", grid_mlp.best_params_)

from sklearn.neural_network import MLPClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, classification_report, confusion_matrix, ConfusionMatrixDisplay
import matplotlib.pyplot as plt

# 1. Préparation des données
X = datatrainset_clean[features]
y = datatrainset_clean['is_generous'].astype(int)

# 2. Split des données
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

# 3. Réseau de neurones
model = MLPClassifier(hidden_layer_sizes=(100,), activation='relu', solver='adam', max_iter=300, random_state=42)
model.fit(X_train, y_train)


# 4. Prédiction
y_pred = model.predict(X_test)
y_proba = model.predict_proba(X_test)[:, 1]

# 5. Évaluation
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred)
recall = recall_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred)
roc_auc = roc_auc_score(y_test, y_proba)

# Affichage des métriques
print("╔════════════════════════════════════╗")
print("║ MÉTRIQUES DU RÉSEAU DE NEURONES    ║")
print("╠════════════════════════════════════╣")
print(f"║ Accuracy: {accuracy:.4f}                   ║")
print(f"║ Precision: {precision:.4f}                  ║")
print(f"║ Recall: {recall:.4f}                     ║")
print(f"║ F1-score: {f1:.4f}                   ║")
print(f"║ ROC AUC: {roc_auc:.4f}                    ║")
print("╚════════════════════════════════════╝")

print("\nClassification Report détaillé:")
print(classification_report(y_test, y_pred))

# Matrice de confusion
cm = confusion_matrix(y_test, y_pred)
disp = ConfusionMatrixDisplay(confusion_matrix=cm)
disp.plot(cmap='Purples')
plt.title("Matrice de Confusion - Réseau de Neurones")
plt.show()

from sklearn.ensemble import RandomForestClassifier

# 1. Préparation des données
X = datatrainset_clean[features]
y = datatrainset_clean['is_generous'].astype(int)

# 2. Split des données
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

# 3. Modèle Random Forest
model = RandomForestClassifier(n_estimators=100, random_state=42)
model.fit(X_train, y_train)


# 4. Prédiction
y_pred = model.predict(X_test)
y_proba = model.predict_proba(X_test)[:, 1]

# 5. Évaluation
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred)
recall = recall_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred)
roc_auc = roc_auc_score(y_test, y_proba)

# Affichage
print("╔════════════════════════════════════╗")
print("║ MÉTRIQUES DU MODÈLE RANDOM FOREST  ║")
print("╠════════════════════════════════════╣")
print(f"║ Accuracy: {accuracy:.4f}                   ║")
print(f"║ Precision: {precision:.4f}                  ║")
print(f"║ Recall: {recall:.4f}                     ║")
print(f"║ F1-score: {f1:.4f}                   ║")
print(f"║ ROC AUC: {roc_auc:.4f}                    ║")
print("╚════════════════════════════════════╝")

print("\nClassification Report détaillé:")
print(classification_report(y_test, y_pred))

# Matrice de confusion
cm = confusion_matrix(y_test, y_pred)
disp = ConfusionMatrixDisplay(confusion_matrix=cm)
disp.plot(cmap='Greens')
plt.title("Matrice de Confusion - Random Forest")
plt.show()


params_rf = {
    'n_estimators': [50, 100, 200],
    'max_depth': [None, 5, 10],
    'min_samples_split': [2, 5]
}
grid_rf = GridSearchCV(RandomForestClassifier(random_state=42), params_rf, cv=5, scoring='f1')
grid_rf.fit(X_train, y_train)
print("Meilleurs hyperparamètres RF:", grid_rf.best_params_)


# 1. Préparation des données
X = datatrainset_clean[features]
y = datatrainset_clean['is_generous'].astype(int)

# 2. Split des données
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

# 3. Modèle Random Forest
model = RandomForestClassifier(n_estimators=100, random_state=42)
model.fit(X_train, y_train)


# 4. Prédiction
y_pred = model.predict(X_test)
y_proba = model.predict_proba(X_test)[:, 1]

# 5. Évaluation
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred)
recall = recall_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred)
roc_auc = roc_auc_score(y_test, y_proba)

# Affichage
print("╔════════════════════════════════════╗")
print("║ MÉTRIQUES DU MODÈLE RANDOM FOREST  ║")
print("╠════════════════════════════════════╣")
print(f"║ Accuracy: {accuracy:.4f}                   ║")
print(f"║ Precision: {precision:.4f}                  ║")
print(f"║ Recall: {recall:.4f}                     ║")
print(f"║ F1-score: {f1:.4f}                   ║")
print(f"║ ROC AUC: {roc_auc:.4f}                    ║")
print("╚════════════════════════════════════╝")

print("\nClassification Report détaillé:")
print(classification_report(y_test, y_pred))

# Matrice de confusion
cm = confusion_matrix(y_test, y_pred)
disp = ConfusionMatrixDisplay(confusion_matrix=cm)
disp.plot(cmap='Greens')
plt.title("Matrice de Confusion - Random Forest")
plt.show()


from sklearn.model_selection import train_test_split
from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, classification_report, confusion_matrix, ConfusionMatrixDisplay
import matplotlib.pyplot as plt

# 1. Préparation des données
X = datatrainset_clean[features]
y = datatrainset_clean['is_generous'].astype(int)

# 2. Split des données
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

# 3. Modèle KNN
model = KNeighborsClassifier(n_neighbors=5)
model.fit(X_train, y_train)


# 4. Prédiction
y_pred = model.predict(X_test)
y_proba = model.predict_proba(X_test)[:, 1]

# 5. Évaluation
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred)
recall = recall_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred)
roc_auc = roc_auc_score(y_test, y_proba)

# Affichage
print("╔══════════════════════════════╗")
print("║     MÉTRIQUES DU MODÈLE KNN  ║")
print("╠══════════════════════════════╣")
print(f"║ Accuracy: {accuracy:.4f}             ║")
print(f"║ Precision: {precision:.4f}            ║")
print(f"║ Recall: {recall:.4f}               ║")
print(f"║ F1-score: {f1:.4f}             ║")
print(f"║ ROC AUC: {roc_auc:.4f}              ║")
print("╚══════════════════════════════╝")

print("\nClassification Report détaillé:")
print(classification_report(y_test, y_pred))

# Matrice de confusion
cm = confusion_matrix(y_test, y_pred)
disp = ConfusionMatrixDisplay(confusion_matrix=cm)
disp.plot(cmap='Oranges')
plt.title("Matrice de Confusion - KNN")
plt.show()

from sklearn.model_selection import GridSearchCV

params_knn = {'n_neighbors': [3, 5, 7, 9 , 11 , 13 , 15 , 19 ]}
grid_knn = GridSearchCV(KNeighborsClassifier(), params_knn, cv=5, scoring='f1')
grid_knn.fit(X_train, y_train)
print("Meilleur K pour KNN:", grid_knn.best_params_)

# 1. Préparation des données
X = datatrainset_clean[features]
y = datatrainset_clean['is_generous'].astype(int)

# 2. Split des données
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

# 3. Modèle KNN
model = KNeighborsClassifier(n_neighbors=19)
model.fit(X_train, y_train)


# 4. Prédiction
y_pred = model.predict(X_test)
y_proba = model.predict_proba(X_test)[:, 1]

# 5. Évaluation
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred)
recall = recall_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred)
roc_auc = roc_auc_score(y_test, y_proba)

# Affichage
print("╔══════════════════════════════╗")
print("║     MÉTRIQUES DU MODÈLE KNN  ║")
print("╠══════════════════════════════╣")
print(f"║ Accuracy: {accuracy:.4f}             ║")
print(f"║ Precision: {precision:.4f}            ║")
print(f"║ Recall: {recall:.4f}               ║")
print(f"║ F1-score: {f1:.4f}             ║")
print(f"║ ROC AUC: {roc_auc:.4f}              ║")
print("╚══════════════════════════════╝")

print("\nClassification Report détaillé:")
print(classification_report(y_test, y_pred))

# Matrice de confusion
cm = confusion_matrix(y_test, y_pred)
disp = ConfusionMatrixDisplay(confusion_matrix=cm)
disp.plot(cmap='Oranges')
plt.title("Matrice de Confusion - KNN")
plt.show()


X = datatrainset_clean[features]
y = datatrainset_clean['is_generous'].astype(int)  # Conversion en entier

# 2. Split des données
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

# 3. Création et entraînement du modèle (version corrigée)
model = LogisticRegression(max_iter=1000, penalty=None, solver='lbfgs')  # penalty=None au lieu de 'none'
model.fit(X_train, y_train)


y_pred = model.predict(X_test)
y_proba = model.predict_proba(X_test)[:, 1]  # Probabilités pour la classe positive

# Calcul des métriques
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred)
recall = recall_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred)
roc_auc = roc_auc_score(y_test, y_proba)

# Affichage des métriques
print("╔══════════════════════════════╗")
print("║       MÉTRIQUES DU MODÈLE    ║")
print("╠══════════════════════════════╣")
print(f"║ Accuracy: {accuracy:.4f}             ║")
print(f"║ Precision: {precision:.4f}            ║")
print(f"║ Recall: {recall:.4f}               ║")
print(f"║ F1-score: {f1:.4f}             ║")
print(f"║ ROC AUC: {roc_auc:.4f}              ║")
print("╚══════════════════════════════╝")

print("\nClassification Report détaillé:")
print(classification_report(y_test, y_pred))

# 5. Matrice de confusion
cm = confusion_matrix(y_test, y_pred)
disp = ConfusionMatrixDisplay(confusion_matrix=cm)
disp.plot(cmap='Blues')
plt.title("Matrice de Confusion - Régression Logistique")
plt.show()

# 6. Visualisation avec PCA (reste identique)
pca = PCA(n_components=2)
X_pca = pca.fit_transform(X)

X_train_pca = pca.transform(X_train)
X_test_pca = pca.transform(X_test)

x_min, x_max = X_pca[:, 0].min() - 1, X_pca[:, 0].max() + 1
y_min, y_max = X_pca[:, 1].min() - 1, X_pca[:, 1].max() + 1
xx, yy = np.meshgrid(np.linspace(x_min, x_max, 100),
                     np.linspace(y_min, y_max, 100))


params_log = {
    'C': [0.01, 0.1, 1, 10],
    'penalty': ['l1', 'l2'],
    'solver': ['liblinear']
}
grid_log = GridSearchCV(LogisticRegression(max_iter=1000), params_log, cv=5, scoring='f1')
grid_log.fit(X_train, y_train)


X = datatrainset_clean[features]
y = datatrainset_clean['is_generous'].astype(int)  # Conversion en entier

# 2. Split des données
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

# 3. Création et entraînement du modèle (version corrigée)
model = LogisticRegression(max_iter=1000, penalty=None, solver='lbfgs')  # penalty=None au lieu de 'none'
model.fit(X_train, y_train)


y_pred = model.predict(X_test)
y_proba = model.predict_proba(X_test)[:, 1]  # Probabilités pour la classe positive

# Calcul des métriques
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred)
recall = recall_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred)
roc_auc = roc_auc_score(y_test, y_proba)

# Affichage des métriques
print("╔══════════════════════════════╗")
print("║       MÉTRIQUES DU MODÈLE    ║")
print("╠══════════════════════════════╣")
print(f"║ Accuracy: {accuracy:.4f}             ║")
print(f"║ Precision: {precision:.4f}            ║")
print(f"║ Recall: {recall:.4f}               ║")
print(f"║ F1-score: {f1:.4f}             ║")
print(f"║ ROC AUC: {roc_auc:.4f}              ║")
print("╚══════════════════════════════╝")

print("\nClassification Report détaillé:")
print(classification_report(y_test, y_pred))

# 5. Matrice de confusion
cm = confusion_matrix(y_test, y_pred)
disp = ConfusionMatrixDisplay(confusion_matrix=cm)
disp.plot(cmap='Blues')
plt.title("Matrice de Confusion - Régression Logistique")
plt.show()

# 6. Visualisation avec PCA (reste identique)
pca = PCA(n_components=2)
X_pca = pca.fit_transform(X)

X_train_pca = pca.transform(X_train)
X_test_pca = pca.transform(X_test)

x_min, x_max = X_pca[:, 0].min() - 1, X_pca[:, 0].max() + 1
y_min, y_max = X_pca[:, 1].min() - 1, X_pca[:, 1].max() + 1
xx, yy = np.meshgrid(np.linspace(x_min, x_max, 100),
                     np.linspace(y_min, y_max, 100))



X = datatrainset_clean[features]
y = datatrainset_clean['is_generous'].astype(int)

# 2. Split des données
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

# 3. Création et entraînement du modèle
model = DecisionTreeClassifier(
    max_depth=3,
    min_samples_split=20,
    criterion='gini',
    random_state=42
)
model.fit(X_train, y_train)


y_pred = model.predict(X_test)
y_proba = model.predict_proba(X_test)[:, 1]

# Calcul des métriques
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred)
recall = recall_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred)
roc_auc = roc_auc_score(y_test, y_proba)

# Affichage des métriques
print("╔══════════════════════════════╗")
print("║       MÉTRIQUES DU MODÈLE    ║")
print("╠══════════════════════════════╣")
print(f"║ Accuracy: {accuracy:.4f}             ║")
print(f"║ Precision: {precision:.4f}            ║")
print(f"║ Recall: {recall:.4f}               ║")
print(f"║ F1-score: {f1:.4f}             ║")
print(f"║ ROC AUC: {roc_auc:.4f}              ║")
print("╚══════════════════════════════╝")

print("\nClassification Report détaillé:")
print(classification_report(y_test, y_pred))

# 5. Matrice de confusion
cm = confusion_matrix(y_test, y_pred)
disp = ConfusionMatrixDisplay(confusion_matrix=cm)
disp.plot(cmap='Blues')
plt.title("Matrice de Confusion - Arbre de Décision")
plt.show()

# 6. Visualisation de l'arbre (version texte)
tree_rules = export_text(model, feature_names=features)
print("\nStructure de l'arbre (version texte):")
print(tree_rules)

# 7. Visualisation graphique de l'arbre
plt.figure(figsize=(20, 10))
plot_tree(model,
          feature_names=features,
          class_names=['Non Généreux', 'Généreux'],
          filled=True,
          rounded=True,
          proportion=True)
plt.title("Structure de l'arbre de décision")
plt.show()

# 8. Importance des features
plt.figure(figsize=(10, 5))
importances = pd.Series(model.feature_importances_, index=features)
importances.sort_values().plot.barh(color='skyblue')
plt.title('Importance des variables')
plt.xlabel('Score d\'importance')
plt.grid(axis='x', linestyle='--', alpha=0.6)
plt.show()



from sklearn.tree import DecisionTreeClassifier

params_tree = {
    'max_depth': [None, 3, 5, 10],
    'min_samples_split': [2, 5, 10],
    'criterion': ['gini', 'entropy']
}
grid_tree = GridSearchCV(DecisionTreeClassifier(random_state=42), params_tree, cv=5, scoring='f1')
grid_tree.fit(X_train, y_train)

print("Arbre de Décision - Meilleurs paramètres :", grid_tree.best_params_)
best_tree = grid_tree.best_estimator_


X = datatrainset_clean[features]
y = datatrainset_clean['is_generous'].astype(int)

# 2. Split des données
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

# 3. Création et entraînement du modèle
model = DecisionTreeClassifier(
    max_depth=3,
    min_samples_split=2,
    criterion='entropy',
    random_state=42
)
model.fit(X_train, y_train)


y_pred = model.predict(X_test)
y_proba = model.predict_proba(X_test)[:, 1]

# Calcul des métriques
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred)
recall = recall_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred)
roc_auc = roc_auc_score(y_test, y_proba)

# Affichage des métriques
print("╔══════════════════════════════╗")
print("║       MÉTRIQUES DU MODÈLE    ║")
print("╠══════════════════════════════╣")
print(f"║ Accuracy: {accuracy:.4f}             ║")
print(f"║ Precision: {precision:.4f}            ║")
print(f"║ Recall: {recall:.4f}               ║")
print(f"║ F1-score: {f1:.4f}             ║")
print(f"║ ROC AUC: {roc_auc:.4f}              ║")
print("╚══════════════════════════════╝")

print("\nClassification Report détaillé:")
print(classification_report(y_test, y_pred))

# 5. Matrice de confusion
cm = confusion_matrix(y_test, y_pred)
disp = ConfusionMatrixDisplay(confusion_matrix=cm)
disp.plot(cmap='Blues')
plt.title("Matrice de Confusion - Arbre de Décision")
plt.show()

# 6. Visualisation de l'arbre (version texte)
tree_rules = export_text(model, feature_names=features)
print("\nStructure de l'arbre (version texte):")
print(tree_rules)

# 7. Visualisation graphique de l'arbre
plt.figure(figsize=(20, 10))
plot_tree(model,
          feature_names=features,
          class_names=['Non Généreux', 'Généreux'],
          filled=True,
          rounded=True,
          proportion=True)
plt.title("Structure de l'arbre de décision")
plt.show()

# 8. Importance des features
plt.figure(figsize=(10, 5))
importances = pd.Series(model.feature_importances_, index=features)
importances.sort_values().plot.barh(color='skyblue')
plt.title('Importance des variables')
plt.xlabel('Score d\'importance')
plt.grid(axis='x', linestyle='--', alpha=0.6)
plt.show()




X = datatrainset_clean[features]
y = datatrainset_clean['is_generous'].astype(int)

# 2. Split des données
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

# 3. Création et entraînement du modèle
model = GaussianNB()
model.fit(X_train, y_train)



# 4. Évaluation et métriques
y_pred = model.predict(X_test)
y_proba = model.predict_proba(X_test)[:, 1]

# Calcul des métriques
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred)
recall = recall_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred)
roc_auc = roc_auc_score(y_test, y_proba)

# Affichage des métriques
print("╔══════════════════════════════╗")
print("║       MÉTRIQUES DU MODÈLE    ║")
print("╠══════════════════════════════╣")
print(f"║ Accuracy: {accuracy:.4f}             ║")
print(f"║ Precision: {precision:.4f}            ║")
print(f"║ Recall: {recall:.4f}               ║")
print(f"║ F1-score: {f1:.4f}             ║")
print(f"║ ROC AUC: {roc_auc:.4f}              ║")
print("╚══════════════════════════════╝")

print("\nClassification Report détaillé:")
print(classification_report(y_test, y_pred))

# 5. Matrice de confusion
cm = confusion_matrix(y_test, y_pred)
disp = ConfusionMatrixDisplay(confusion_matrix=cm)
disp.plot(cmap='Blues')
plt.title("Matrice de Confusion - Bayes Naïf")
plt.show()

from sklearn.model_selection import GridSearchCV

params_knn = {'n_neighbors': [3, 5, 7, 9]}
grid_knn = GridSearchCV(KNeighborsClassifier(), params_knn, cv=5, scoring='f1')
grid_knn.fit(X_train, y_train)
print("Meilleur K pour KNN:", grid_knn.best_params_)


X = datatrainset_clean[features]
y = datatrainset_clean['is_generous'].astype(int)

# 2. Split des données
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

# 3. Création et entraînement du modèle
model = GaussianNB()
model.fit(X_train, y_train)



# 4. Évaluation et métriques
y_pred = model.predict(X_test)
y_proba = model.predict_proba(X_test)[:, 1]

# Calcul des métriques
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred)
recall = recall_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred)
roc_auc = roc_auc_score(y_test, y_proba)

# Affichage des métriques
print("╔══════════════════════════════╗")
print("║       MÉTRIQUES DU MODÈLE    ║")
print("╠══════════════════════════════╣")
print(f"║ Accuracy: {accuracy:.4f}             ║")
print(f"║ Precision: {precision:.4f}            ║")
print(f"║ Recall: {recall:.4f}               ║")
print(f"║ F1-score: {f1:.4f}             ║")
print(f"║ ROC AUC: {roc_auc:.4f}              ║")
print("╚══════════════════════════════╝")

print("\nClassification Report détaillé:")
print(classification_report(y_test, y_pred))

# 5. Matrice de confusion
cm = confusion_matrix(y_test, y_pred)
disp = ConfusionMatrixDisplay(confusion_matrix=cm)
disp.plot(cmap='Blues')
plt.title("Matrice de Confusion - Bayes Naïf")
plt.show()

datatestset=extrairedetime(datatestset)
datatestset_clean=nettoyage(datatestset)
datatestset_clean=encodage(datatestset_clean)
datatestset_clean=FeatureEngineering(datatrainset_clean)
numerical_cols = datatestset_clean.select_dtypes(include=['float64', 'int64']).columns
print("Numerical columns:", numerical_cols)
scaler = StandardScaler()
numeric_columns = datatestset_clean.select_dtypes(include=['float', 'int']).columns
datatestset_clean[numeric_columns] = scaler.transform(datatestset_clean[numeric_columns])
print(datatestset_clean.head())

features = ['pickup_week', 'pickup_month', 'fare_amount',
            'trip_distance', 'RatecodeID', 'payment_type', 'speed_mph']

X = datatestset_clean[features]
y = datatestset_clean['is_generous'].astype(int)  

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

model = svm.SVC(kernel='rbf', C=1.0, gamma='scale', probability=True)
model.fit(X_train, y_train)

y_pred = model.predict(X_test)
y_proba = model.predict_proba(X_test)[:, 1]  # Probabilités pour la classe positive

# Calcul des métriques
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred)
recall = recall_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred)
roc_auc = roc_auc_score(y_test, y_proba)

# Affichage des métriques avec une mise en forme claire
print("╔══════════════════════════════╗")
print("║       MÉTRIQUES DU MODÈLE    ║")
print("╠══════════════════════════════╣")
print(f"║ Accuracy: {accuracy:.4f}             ║")
print(f"║ Precision: {precision:.4f}            ║")
print(f"║ Recall: {recall:.4f}               ║")
print(f"║ F1-score: {f1:.4f}             ║")
print(f"║ ROC AUC: {roc_auc:.4f}              ║")
print("╚══════════════════════════════╝")

print("\nClassification Report détaillé:")
print(classification_report(y_test, y_pred))


# 5. Matrice de confusion
cm = confusion_matrix(y_test, y_pred)
disp = ConfusionMatrixDisplay(confusion_matrix=cm)
disp.plot(cmap='Blues')
plt.title("Matrice de Confusion")
plt.show()


X = datatestset_clean[features]
y = datatestset_clean['is_generous'].astype(int)  # Conversion en entier

# 2. Split des données
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

# 3. Création et entraînement du modèle (version corrigée)
model = LogisticRegression(max_iter=1000, penalty=None, solver='lbfgs')  # penalty=None au lieu de 'none'
model.fit(X_train, y_train)


y_pred = model.predict(X_test)
y_proba = model.predict_proba(X_test)[:, 1]  # Probabilités pour la classe positive

# Calcul des métriques
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred)
recall = recall_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred)
roc_auc = roc_auc_score(y_test, y_proba)

# Affichage des métriques
print("╔══════════════════════════════╗")
print("║       MÉTRIQUES DU MODÈLE    ║")
print("╠══════════════════════════════╣")
print(f"║ Accuracy: {accuracy:.4f}             ║")
print(f"║ Precision: {precision:.4f}            ║")
print(f"║ Recall: {recall:.4f}               ║")
print(f"║ F1-score: {f1:.4f}             ║")
print(f"║ ROC AUC: {roc_auc:.4f}              ║")
print("╚══════════════════════════════╝")

print("\nClassification Report détaillé:")
print(classification_report(y_test, y_pred))

# 5. Matrice de confusion
cm = confusion_matrix(y_test, y_pred)
disp = ConfusionMatrixDisplay(confusion_matrix=cm)
disp.plot(cmap='Blues')
plt.title("Matrice de Confusion - Régression Logistique")
plt.show()



X = datatestset_clean[features]
y = datatestset_clean['is_generous'].astype(int)

# 2. Split des données
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

# 3. Création et entraînement du modèle
model = DecisionTreeClassifier(
    max_depth=3,
    min_samples_split=20,
    criterion='gini',
    random_state=42
)
model.fit(X_train, y_train)


y_pred = model.predict(X_test)
y_proba = model.predict_proba(X_test)[:, 1]

# Calcul des métriques
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred)
recall = recall_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred)
roc_auc = roc_auc_score(y_test, y_proba)

# Affichage des métriques
print("╔══════════════════════════════╗")
print("║       MÉTRIQUES DU MODÈLE    ║")
print("╠══════════════════════════════╣")
print(f"║ Accuracy: {accuracy:.4f}             ║")
print(f"║ Precision: {precision:.4f}            ║")
print(f"║ Recall: {recall:.4f}               ║")
print(f"║ F1-score: {f1:.4f}             ║")
print(f"║ ROC AUC: {roc_auc:.4f}              ║")
print("╚══════════════════════════════╝")

print("\nClassification Report détaillé:")
print(classification_report(y_test, y_pred))

# 5. Matrice de confusion
cm = confusion_matrix(y_test, y_pred)
disp = ConfusionMatrixDisplay(confusion_matrix=cm)
disp.plot(cmap='Blues')
plt.title("Matrice de Confusion - Arbre de Décision")
plt.show()

# 6. Visualisation de l'arbre (version texte)
tree_rules = export_text(model, feature_names=features)
print("\nStructure de l'arbre (version texte):")
print(tree_rules)

# 7. Visualisation graphique de l'arbre
plt.figure(figsize=(20, 10))
plot_tree(model,
          feature_names=features,
          class_names=['Non Généreux', 'Généreux'],
          filled=True,
          rounded=True,
          proportion=True)
plt.title("Structure de l'arbre de décision")
plt.show()

# 8. Importance des features
plt.figure(figsize=(10, 5))
importances = pd.Series(model.feature_importances_, index=features)
importances.sort_values().plot.barh(color='skyblue')
plt.title('Importance des variables')
plt.xlabel('Score d\'importance')
plt.grid(axis='x', linestyle='--', alpha=0.6)
plt.show()

X = datatestset_clean[features]
y = datatestset_clean['is_generous'].astype(int)

# 2. Split des données
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

# 3. Création et entraînement du modèle
model = GaussianNB()
model.fit(X_train, y_train)



# 4. Évaluation et métriques
y_pred = model.predict(X_test)
y_proba = model.predict_proba(X_test)[:, 1]

# Calcul des métriques
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred)
recall = recall_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred)
roc_auc = roc_auc_score(y_test, y_proba)

# Affichage des métriques
print("╔══════════════════════════════╗")
print("║       MÉTRIQUES DU MODÈLE    ║")
print("╠══════════════════════════════╣")
print(f"║ Accuracy: {accuracy:.4f}             ║")
print(f"║ Precision: {precision:.4f}            ║")
print(f"║ Recall: {recall:.4f}               ║")
print(f"║ F1-score: {f1:.4f}             ║")
print(f"║ ROC AUC: {roc_auc:.4f}              ║")
print("╚══════════════════════════════╝")

print("\nClassification Report détaillé:")
print(classification_report(y_test, y_pred))

# 5. Matrice de confusion
cm = confusion_matrix(y_test, y_pred)
disp = ConfusionMatrixDisplay(confusion_matrix=cm)
disp.plot(cmap='Blues')
plt.title("Matrice de Confusion - Bayes Naïf")
plt.show()

from sklearn.model_selection import train_test_split
from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import accuracy_score, precision_score,recall_score, f1_score, roc_auc_score, classification_report, confusion_matrix, ConfusionMatrixDisplay
import matplotlib.pyplot as plt

# 1. Préparation des données
X = datatestset_clean[features]
y = datatestset_clean['is_generous'].astype(int)

# 2. Split des données
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

# 3. Modèle KNN
model = KNeighborsClassifier(n_neighbors=9)
model.fit(X_train, y_train)


# 4. Prédiction
y_pred = model.predict(X_test)
y_proba = model.predict_proba(X_test)[:, 1]

# 5. Évaluation
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred)
recall = recall_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred)
roc_auc = roc_auc_score(y_test, y_proba)

# Affichage
print("╔══════════════════════════════╗")
print("║     MÉTRIQUES DU MODÈLE KNN  ║")
print("╠══════════════════════════════╣")
print(f"║ Accuracy: {accuracy:.4f}             ║")
print(f"║ Precision: {precision:.4f}            ║")
print(f"║ Recall: {recall:.4f}               ║")
print(f"║ F1-score: {f1:.4f}             ║")
print(f"║ ROC AUC: {roc_auc:.4f}              ║")
print("╚══════════════════════════════╝")

print("\nClassification Report détaillé:")
print(classification_report(y_test, y_pred))

# Matrice de confusion
cm = confusion_matrix(y_test, y_pred)
disp = ConfusionMatrixDisplay(confusion_matrix=cm)
disp.plot(cmap='Oranges')
plt.title("Matrice de Confusion - KNN")
plt.show()


from sklearn.model_selection import GridSearchCV

params_knn = {'n_neighbors': [3, 5, 7, 9]}
grid_knn = GridSearchCV(KNeighborsClassifier(), params_knn, cv=5, scoring='f1')
grid_knn.fit(X_train, y_train)
print("Meilleur K pour KNN:", grid_knn.best_params_)

from sklearn.ensemble import RandomForestClassifier

# 1. Préparation des données
X = datatestset_clean[features]
y = datatestset_clean['is_generous'].astype(int)

# 2. Split des données
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

# 3. Modèle Random Forest
model = RandomForestClassifier(n_estimators=100, random_state=42)
model.fit(X_train, y_train)

# 4. Prédiction
y_pred = model.predict(X_test)
y_proba = model.predict_proba(X_test)[:, 1]

# 5. Évaluation
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred)
recall = recall_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred)
roc_auc = roc_auc_score(y_test, y_proba)

# Affichage
print("╔════════════════════════════════════╗")
print("║ MÉTRIQUES DU MODÈLE RANDOM FOREST  ║")
print("╠════════════════════════════════════╣")
print(f"║ Accuracy: {accuracy:.4f}                   ║")
print(f"║ Precision: {precision:.4f}                  ║")
print(f"║ Recall: {recall:.4f}                     ║")
print(f"║ F1-score: {f1:.4f}                   ║")
print(f"║ ROC AUC: {roc_auc:.4f}                    ║")
print("╚════════════════════════════════════╝")

print("\nClassification Report détaillé:")
print(classification_report(y_test, y_pred))

# Matrice de confusion
cm = confusion_matrix(y_test, y_pred)
disp = ConfusionMatrixDisplay(confusion_matrix=cm)
disp.plot(cmap='Greens')
plt.title("Matrice de Confusion - Random Forest")
plt.show()


from sklearn.neural_network import MLPClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, classification_report, confusion_matrix, ConfusionMatrixDisplay
import matplotlib.pyplot as plt

# 1. Préparation des données
X = datatestset_clean[features]
y = datatestset_clean['is_generous'].astype(int)

# 2. Split des données
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

# 3. Réseau de neurones
model = MLPClassifier(hidden_layer_sizes=(100,), activation='relu', solver='adam', max_iter=300, random_state=42)
model.fit(X_train, y_train)



# 4. Prédiction
y_pred = model.predict(X_test)
y_proba = model.predict_proba(X_test)[:, 1]

# 5. Évaluation
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred)
recall = recall_score(y_test, y_pred)
f1 = f1_score(y_test, y_pred)
roc_auc = roc_auc_score(y_test, y_proba)

# Affichage des métriques
print("╔════════════════════════════════════╗")
print("║ MÉTRIQUES DU RÉSEAU DE NEURONES    ║")
print("╠════════════════════════════════════╣")
print(f"║ Accuracy: {accuracy:.4f}                   ║")
print(f"║ Precision: {precision:.4f}                  ║")
print(f"║ Recall: {recall:.4f}                     ║")
print(f"║ F1-score: {f1:.4f}                   ║")
print(f"║ ROC AUC: {roc_auc:.4f}                    ║")
print("╚════════════════════════════════════╝")

print("\nClassification Report détaillé:")
print(classification_report(y_test, y_pred))

# Matrice de confusion
cm = confusion_matrix(y_test, y_pred)
disp = ConfusionMatrixDisplay(confusion_matrix=cm)
disp.plot(cmap='Purples')
plt.title("Matrice de Confusion - Réseau de Neurones")
plt.show()

